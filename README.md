
# BlackJack Game

## **Overview**

Welcome to my BlackJack game! This project is a simple implementation of the classic BlackJack card game, built using python and some library. 
The game follows standard BlackJack rules, allowing the player to compete against the dealer or even being the dealer

## **Features**

- **Interactive Gameplay:** Point and click gameplay
- **Dealer AI:** Bo<PERSON> follows basic BlackJack rules to decide when to hit or stand.
- **Multiple Rounds:** Players can play multiple rounds without restarting the game.
- **Restart with old Money:** Players can play new game with previous money they got
- **Player can choose role:** Players can choose be a dealer or player
  
## **Technologies Used**

- **Programming Language:** Python
- **Additional Libraries/Frameworks:** Pygame, sys , random , math , pyvidplayer2
- **Version Control:** Git

## **How to Play**

1. **Clone the Repository:**
    ```bash
    git clone https://github.com/uwu192/Blackjack.git
    cd Blackjack
    ```

2. **Run the Game:**
    - In folder dist, double click Game.exe to play

3. **Gameplay:**
    - Players will choose to be a dealer or normal player
    - Players will start with a certain amount of chips. (10k)
    - Place your bet at the beginning of each round.
    # Player
        - Choose to "Hit" to receive another card or "Stand" to keep your current hand.
        - Try to beat the dealer by getting closer to 21 without exceeding it.
    # Dealer
        - Choose to "Show" Bot's card to challenging it and Dealer can "Hit" while challenging
        - Choose a safe cards to win as much as possible
  - Play new round until boring or debt
    
## **Installation**

1. **Prerequisites:**
    - At least python 3.9
    - lib using or else bug: sys , random , math , pyvidplayer2
  
2. **Dependencies:**
    - Install dependencies using the following command:
      ```bash
      pip install sys
      pip install random
      pip install math
      pip install pyvidplayer2
      ```

## **Future Improvements**

- **Easier to download:** Plan to transform an app can execute and don't need to download anything else
- **Multiplayer Mode:** Adding the ability to play with multiple players over a network.
- **Advanced Dealer AI:** Improve the dealer's decision-making with more advanced strategies or taking risks.
- **Easier to read:** Modify variable, class and subprogram for contributor 

## **Contributing**

Contributions are welcome! Feel free to "cook" this repository together, make your changes, and submit a pull request.

## **License**

This project is licensed under the "Nothing". You can clone and do a new version yourself |Make it different|

## **Contact**

For any questions or suggestions, feel free to reach out:

- **Email:** <EMAIL>
- **GitHub:** https://github.com/uwu192
